# ESP32 控制 LED 灯珠亮灭

## AI 提示词

### 实现 LED 灯间隔一秒闪烁

我使用的开发板是 esp32-s3-devkitc-1
我在开发板的21引脚处连接了一个led灯，led灯的负极连接开发板的GND
现在我希望你修改 main.c 文件，实现间隔一秒闪烁led灯，即：
持续一秒点亮led
持续一秒熄灭led
你应该需要调整高低电平实现这一功能
另外，我希型你将 main.c 调整成使用 C++ 代码实现，你应该提供一个 LightController 的控制器类

### 实现 开关 控制 LED 灯亮灭

我的开发板是ESP32-S3-Devkitc-1
我现在在 开发板的 41 引脚处连接了一个按钮，按钮的正极连接3v3电源输出，负极连接到41引脚处
也就是说，按钮默认是低电平状态
现在我希望你修改 main.cpp 中的代码，实现一个 ButtonController 类
实现的功能是：当我按下按钮时点亮LED灯，当我释放按钮时熄灭LED灯
# ESP32-S3 语音命令识别示例配置文件
# 本配置文件针对ESP32-S3开发板优化，支持语音唤醒和命令词识别功能
# 使用INMP441数字麦克风和MAX98357A功放进行音频输入输出
#
CONFIG_IDF_TARGET="esp32s3"

# Flash配置 - 使用16MB Flash以存储语音模型
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHSIZE_16MB=y

# 分区表配置 - 自定义分区表以支持大容量语音模型存储
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000
CONFIG_PARTITION_TABLE_MD5=y

# ESP-SR语音识别配置
# 模型存储位置：Flash（推荐，启动速度快）
CONFIG_MODEL_IN_FLASH=y

# 音频前端处理配置
CONFIG_AFE_INTERFACE_V1=y
CONFIG_SR_NSN_WEBRTC=y
CONFIG_SR_VADN_VADNET1_MEDIUM=y

# 唤醒词配置 - 默认使用"你好小智"
CONFIG_SR_WN_WN9_NIHAOXIAOZHI_TTS=y

# 命令词识别配置 - 使用MultiNet7中文模型（最新版本）
CONFIG_SR_MN_CN_MULTINET7_QUANT=y
CONFIG_SR_MN_EN_NONE=y

# CPU频率配置 - 240MHz以确保语音处理性能
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y

# 缓存配置 - 优化语音处理性能
CONFIG_ESP32S3_INSTRUCTION_CACHE_32KB=y
CONFIG_ESP32S3_INSTRUCTION_CACHE_8WAYS=y
CONFIG_ESP32S3_INSTRUCTION_CACHE_LINE_32B=y
CONFIG_ESP32S3_DATA_CACHE_64KB=y
CONFIG_ESP32S3_DATA_CACHE_8WAYS=y
CONFIG_ESP32S3_DATA_CACHE_LINE_64B=y

# PSRAM配置 - 8MB外部PSRAM用于语音模型加载
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_TYPE_AUTO=y
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_SPIRAM_BOOT_INIT=y
CONFIG_SPIRAM_USE_MALLOC=y
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=4096
CONFIG_SPIRAM_MALLOC_RESERVE_INTERNAL=49152

# 系统任务配置 - 为语音处理分配足够的栈空间
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=4096
CONFIG_ESP_MAIN_TASK_STACK_SIZE=10240
CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0=y

# FreeRTOS配置
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY=y

# 编译器优化配置 - 优化代码大小以节省Flash空间
CONFIG_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE=y
CONFIG_COMPILER_ORPHAN_SECTIONS_PLACE=y

# 内存管理配置 - 优化实时性能
CONFIG_PERIPH_CTRL_FUNC_IN_IRAM=y
CONFIG_GDMA_CTRL_FUNC_IN_IRAM=y

# 系统错误处理配置
CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT=y

# 日志配置 - 适中的日志级别便于调试
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT=y

# I2S配置优化 - 确保音频数据传输稳定性
CONFIG_SPI_MASTER_ISR_IN_IRAM=y
CONFIG_SPI_SLAVE_ISR_IN_IRAM=y

# 🎤 ESP32-S3 语音唤醒小助手 - 零基础入门版

> **特别为初学者设计** | 即使第一次接触 ESP32 也能轻松上手

**视频教程预告**：我会在 B 站发布详细使用视频（搜索关键词：ESP32 语音唤醒教程）

## 🌟 本示例代码主要做了什么？

这是一个基于 ESP32-S3 和 INMP441 数字麦克风的唤醒词识别示例项目。当你对它说出正确的唤醒词，它就会让 LED 灯闪烁回应你，就像在说："我听到啦！"

**为什么做这个？**  
很多朋友玩过"小智 AI"，但不知道如何自己实现唤醒功能。这个项目展示了最基础的唤醒词实现方式，并且我增加了大量代码注释，即便是基础比较弱的朋友也可以轻松看懂。

## ⚡ 功能特性

- ✅ **完全兼容**小智 AI 的硬件接线方式
- ✅ 提供**详细注释**的代码（每步都解释）
- ✅ 让你可以**快速移植**到自己的项目

## 📦 需要准备什么？

### 硬件清单（淘宝都能买到）

| 部件   | 推荐型号           | 备注               |
| ------ | ------------------ | ------------------ |
| 开发板 | ESP32-S3-DevKitC-1 | 注意要 S3 版本     |
| 麦克风 | INMP441            | 约 5 元/个         |
| LED 灯 | 任意颜色           | 普通发光二极管即可 |

### 接线图（像拼积木一样简单）

```
麦克风 → ESP32开发板
--------------------
VDD（麦克风）→ 3.3V（开发板）  // 接电源正极
GND（麦克风）→ GND（开发板）   // 接电源负极
SD  （麦克风）→ GPIO6         // 数据线
WS  （麦克风）→ GPIO4         // 控制线
SCK （麦克风）→ GPIO5         // 时钟线

LED长脚 → GPIO21        // 信号线
LED短脚 → GND           // 接地
```

> 💡 接线提示：LED 长脚是正极，短脚是负极，接反不会烧坏但灯不亮

## 🚀 3 分钟快速上手

### 方法 1：电脑已安装 ESP-IDF（推荐）

```bash
# 步骤1：打开配置菜单
idf.py menuconfig

# 出现蓝色菜单后：
# ① 用方向键选择 "ESP Speech Recognition"
# ② 按Enter进入下一级菜单
# ③ 用方向键选择 "Load Multiple Wake Words"
# ④ 按Enter选择你喜欢的唤醒词（默认"你好小智"）
# ⑤ 按S保存，按Q退出

# 步骤2：编译代码（约1分钟）
idf.py build

# 步骤3：连接开发板到电脑USB口
idf.py flash      # 自动烧录程序

# 步骤4：查看运行状态
idf.py monitor    # 看到"等待唤醒..."就成功啦！
```

### 方法 2：不用安装软件（在线烧录）

> 为了方便大家使用，我已经在 release 目录编译好了大家常用的一些唤醒词

| 文件名            | 说明                   |
| ----------------- | ---------------------- |
| hiesp.bin         | 使用“Hi ESP”唤醒词     |
| hilexin.bin       | 使用“嗨，乐鑫”唤醒词   |
| nihaoxiaozhi.bin  | 使用“你好，小智”唤醒词 |
| xiaoaitongxue.bin | 使用“小爱同学”唤醒词   |

1. 访问 **IDF 官方烧录工具** → [https://espressif.github.io/esp-launchpad/](https://espressif.github.io/esp-launchpad/)
2. 连接开发板到电脑 USB 口
3. 点击网页上的"DIY"按钮
4. 点击网页上的"Connect"按钮，在跳出的弹窗中选择你的开发板
5. 等待连接成功
6. 在 “DIY” 页面将 “Flash” 改成 0
7. 点击 “DIY” 页面中 “Select File” 中的上传文件按钮，选择本项目中 release 目录下的`.bin`文件
8. 点击 “Program” 开始烧录固件
9. 烧录完成后，点击网页上的"Reset"按钮

> 视频教程会演示完整过程，第一次操作建议跟着视频做

## ⚙️ 自定义你的唤醒系统

### 更换 LED 引脚（比如换到 GPIO15）

1. 用记事本/VSCode 打开 `main/main.cc` 文件
2. 找到第 37 行（有`LED_GPIO`字样的地方）
3. 修改数字即可：

```c
// 原代码：接在GPIO21
#define LED_GPIO GPIO_NUM_21  // ← 把21改成你想要的引脚号
```

### 切换唤醒词（支持乐鑫提供的所有开源唤醒词）

```bash
idf.py menuconfig
```

→ 用方向键选择 `ESP Speech Recognition`
→ 按 `Enter` 进入下一级菜单
→ 用方向键选择 `Load Multiple Wake Words`
→ 按 `Enter` 选择你喜欢的唤醒词（默认"你好小智"）
→ 按 S 保存，按 Q 退出

> ✨ **提示**：更换唤醒词模型后，**不需要手动调整代码**，因为示例代码会自动匹配正确的唤醒词。

### 调整灵敏度（防误触发）

打开 `main/main.cc` 第 151 行：

```c
model_iface_data_t *model_data = wakenet->create(model_name, DET_MODE_90);
//  DET_MODE_90 - 推荐值（平衡型）
//  DET_MODE_95 - 最严格（小智AI默认）
```

## ❓ 新手常见问题

### Q1：LED 灯不亮怎么办？

1. 检查 LED 正负极是否接反（长脚接 GPIO，短脚接 GND）
2. 在代码中确认 GPIO 号是否正确（见上面更换引脚步骤）
3. 尝试换一个 LED 灯（可能灯坏了）

### Q2：麦克风没反应？

```
检测步骤：
1. 开发板是否正常供电（USB口电压不足？换接口试试）
2. 用idf.py monitor查看日志
   - 出现"I2S initialized"表示麦克风正常
   - 出现"Failed to init I2S"检查接线
3. 对着麦克风吹气，看日志是否出现"Audio data received"
```

### Q3：唤醒词不识别？

```
解决方案：
1. 在安静环境下测试（背景噪音会影响）
2. 距离麦克风30厘米内清晰发音
3. 降低灵敏度（修改DET_MODE_90→DET_MODE_80）
4. 检查是否选对了唤醒词模型
```

## 📚 学习资源（适合零基础）

### 代码学习指南

我在代码中添加了**超详细中文注释**，比如在 `main/main.cc` 中：

```c
// [新手注意] 这里是音频处理核心函数
// 采样率：16000Hz = 每秒采集16000个声音点
// 每20ms处理一帧：16000×0.02=320个数据点
void process_audio() {
  // 步骤1：从麦克风读取数据 → 步骤2：交给AI模型 → 步骤3：检查是否唤醒
  ...
}
```

### 推荐学习路径

1. 先成功运行示例（体验成就感 ✨）
2. 阅读 `main/main.cc` 中的注释（有分步骤解析）
3. 尝试修改 LED 闪烁模式（比如改成呼吸灯）
4. 添加新功能（如唤醒后播放声音）

## 🎁 项目结构（了解即可）

```
核心文件是这些：
main/
├── main.cc              # 主程序（95%的代码在这里）
├── bsp_board.cc         # 硬件控制（麦克风/LED）
└── bsp_board.h          # 硬件定义
```

## 📜 开源协议

Apache 2.0 - 可自由用于个人/商业项目 **注明原作者即可**

**🎥 完整视频教程将在 B 站发布**  
**觉得项目有帮助？给个 Star✨ 就是最大鼓励！**  
**遇到问题？可以在 B 站视频中评论或者私信我，我看到都会回复**
